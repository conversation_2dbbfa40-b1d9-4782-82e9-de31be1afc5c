package cn.iocoder.yudao.module.demo.controller.admin.inboundallocationrule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 入库分配规则新增/修改 Request VO")
@Data
public class InboundAllocationRuleSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6102")
    private Long id;

    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "规则名称不能为空")
    private String ruleName;

    @Schema(description = "规则编码", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotEmpty(message = "规则编码不能为空")
    private String ruleCode;

    @Schema(description = "规则类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "规则类型不能为空")
    private String ruleType;

    @Schema(description = "优先级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "优先级不能为空")
    private Integer priority;

    @Schema(description = "规则状态：1-启用，0-禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "规则状态：1-启用，0-禁用不能为空")
    private Integer ruleStatus;

    @Schema(description = "分类")
    private String category;

    @Schema(description = "名称", example = "王五")
    private String name;

    @Schema(description = "仓库名称", example = "李四")
    private String warehouseName;

    @Schema(description = "垛位名称", example = "芋艿")
    private String locationName;

    @Schema(description = "仓库ID", example = "19578")
    private Long warehouseId;

    @Schema(description = "垛位ID", example = "24409")
    private Long locationId;

    @Schema(description = "创建部门ID", example = "1764")
    private Long createDeptId;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

}
