package cn.iocoder.yudao.module.demo.controller.admin.dispatchorder;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.demo.controller.admin.dispatchorder.vo.*;
import cn.iocoder.yudao.module.demo.controller.admin.materialacceptdetail.vo.MaterialAcceptDetailRespVO;
import cn.iocoder.yudao.module.demo.dal.dataobject.dispatchorder.DispatchOrderDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialacceptdetail.MaterialAcceptDetailDO;
import cn.iocoder.yudao.module.demo.service.dispatchorder.DispatchOrderService;
import cn.iocoder.yudao.module.demo.service.materialacceptdetail.MaterialAcceptDetailService;

@Tag(name = "管理后台 - 调令信息")
@RestController
@RequestMapping("/demo/dispatch-order")
@Validated
public class DispatchOrderController {

    @Resource
    private DispatchOrderService dispatchOrderService;
    
    @Resource
    private MaterialAcceptDetailService materialAcceptDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建调令信息")
    @PreAuthorize("@ss.hasPermission('demo:dispatch-order:create')")
    public CommonResult<Long> createDispatchOrder(@Valid @RequestBody DispatchOrderSaveReqVO createReqVO) {
        return success(dispatchOrderService.createDispatchOrder(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新调令信息")
    @PreAuthorize("@ss.hasPermission('demo:dispatch-order:update')")
    public CommonResult<Boolean> updateDispatchOrder(@Valid @RequestBody DispatchOrderSaveReqVO updateReqVO) {
        dispatchOrderService.updateDispatchOrder(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除调令信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('demo:dispatch-order:delete')")
    public CommonResult<Boolean> deleteDispatchOrder(@RequestParam("id") Long id) {
        dispatchOrderService.deleteDispatchOrder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得调令信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('demo:dispatch-order:query')")
    public CommonResult<DispatchOrderRespVO> getDispatchOrder(@RequestParam("id") Long id) {
        DispatchOrderDO dispatchOrder = dispatchOrderService.getDispatchOrder(id);
        DispatchOrderRespVO respVO = BeanUtils.toBean(dispatchOrder, DispatchOrderRespVO.class);
        
        // 查询物资详情列表
        List<MaterialAcceptDetailDO> materialDetails = materialAcceptDetailService.getMaterialAcceptDetailsByDispatchId(id);
        if (materialDetails != null && !materialDetails.isEmpty()) {
            respVO.setMaterialDetails(BeanUtils.toBean(materialDetails, MaterialAcceptDetailRespVO.class));
        }
        
        return success(respVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得调令信息分页")
    @PreAuthorize("@ss.hasPermission('demo:dispatch-order:query')")
    public CommonResult<PageResult<DispatchOrderRespVO>> getDispatchOrderPage(@Valid DispatchOrderPageReqVO pageReqVO) {
        PageResult<DispatchOrderDO> pageResult = dispatchOrderService.getDispatchOrderPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DispatchOrderRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出调令信息 Excel")
    @PreAuthorize("@ss.hasPermission('demo:dispatch-order:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDispatchOrderExcel(@Valid DispatchOrderPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DispatchOrderDO> list = dispatchOrderService.getDispatchOrderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "调令信息.xls", "数据", DispatchOrderRespVO.class,
                        BeanUtils.toBean(list, DispatchOrderRespVO.class));
    }
    
    @GetMapping("/list-order-nos")
    @Operation(summary = "获取未删除的调令单号列表")
    @PreAuthorize("@ss.hasPermission('demo:dispatch-order:query')")
    public CommonResult<List<Map<String, Object>>> listDispatchOrderNos() {
        return success(dispatchOrderService.getActiveDispatchOrderNos());
    }
    
    @PostMapping("/sign")
    @Operation(summary = "签收调令", description = "将调令标记为已签收，记录签收时间和签收人员")
    @Parameter(name = "id", description = "调令ID", required = true)
    @PreAuthorize("@ss.hasPermission('demo:dispatch-order:update')")
    public CommonResult<Boolean> signDispatchOrder(@RequestParam("id") Long id) {
        return success(dispatchOrderService.signDispatchOrder(id));
    }
}