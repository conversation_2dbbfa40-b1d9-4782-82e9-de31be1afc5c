package cn.iocoder.yudao.module.demo.controller.admin.documentcategory;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.demo.controller.admin.documentcategory.vo.*;
import cn.iocoder.yudao.module.demo.dal.dataobject.documentcategory.DocumentCategoryDO;
import cn.iocoder.yudao.module.demo.service.documentcategory.DocumentCategoryService;

@Tag(name = "管理后台 - 文档分类")
@RestController
@RequestMapping("/demo/document-category")
@Validated
public class DocumentCategoryController {

    @Resource
    private DocumentCategoryService documentCategoryService;

    @PostMapping("/create")
    @Operation(summary = "创建文档分类")
    @PreAuthorize("@ss.hasPermission('demo:document-category:create')")
    public CommonResult<Long> createDocumentCategory(@Valid @RequestBody DocumentCategorySaveReqVO createReqVO) {
        return success(documentCategoryService.createDocumentCategory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新文档分类")
    @PreAuthorize("@ss.hasPermission('demo:document-category:update')")
    public CommonResult<Boolean> updateDocumentCategory(@Valid @RequestBody DocumentCategorySaveReqVO updateReqVO) {
        documentCategoryService.updateDocumentCategory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除文档分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('demo:document-category:delete')")
    public CommonResult<Boolean> deleteDocumentCategory(@RequestParam("id") Long id) {
        documentCategoryService.deleteDocumentCategory(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得文档分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('demo:document-category:query')")
    public CommonResult<DocumentCategoryRespVO> getDocumentCategory(@RequestParam("id") Long id) {
        DocumentCategoryDO documentCategory = documentCategoryService.getDocumentCategory(id);
        return success(BeanUtils.toBean(documentCategory, DocumentCategoryRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得文档分类列表")
    @PreAuthorize("@ss.hasPermission('demo:document-category:query')")
    public CommonResult<List<DocumentCategoryRespVO>> getDocumentCategoryList(@Valid DocumentCategoryListReqVO listReqVO) {
        List<DocumentCategoryDO> list = documentCategoryService.getDocumentCategoryList(listReqVO);
        return success(BeanUtils.toBean(list, DocumentCategoryRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出文档分类 Excel")
    @PreAuthorize("@ss.hasPermission('demo:document-category:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDocumentCategoryExcel(@Valid DocumentCategoryListReqVO listReqVO,
              HttpServletResponse response) throws IOException {
        List<DocumentCategoryDO> list = documentCategoryService.getDocumentCategoryList(listReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "文档分类.xls", "数据", DocumentCategoryRespVO.class,
                        BeanUtils.toBean(list, DocumentCategoryRespVO.class));
    }

}