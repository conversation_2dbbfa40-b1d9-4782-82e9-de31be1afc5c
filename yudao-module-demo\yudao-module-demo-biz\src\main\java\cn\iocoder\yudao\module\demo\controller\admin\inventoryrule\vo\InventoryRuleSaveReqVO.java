package cn.iocoder.yudao.module.demo.controller.admin.inventoryrule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 盘点规则新增/修改 Request VO")
@Data
public class InventoryRuleSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15143")
    private Long id;

    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "规则名称不能为空")
    private String ruleName;

    @Schema(description = "规则编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "规则编码不能为空")
    private String ruleCode;

    @Schema(description = "判断类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "判断类型不能为空")
    private String judgeType;

    @Schema(description = "盘点周期数", requiredMode = Schema.RequiredMode.REQUIRED, example = "26988")
    @NotNull(message = "盘点周期数不能为空")
    private Integer inventoryCycleCount;

    @Schema(description = "判断周期类型（日、周、月）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "判断周期类型（日、周、月）不能为空")
    private String cycleType;

    @Schema(description = "创建人部门ID", example = "24556")
    private Long createDeptId;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "最后一次执行时间")
    private LocalDateTime lastTime;

}