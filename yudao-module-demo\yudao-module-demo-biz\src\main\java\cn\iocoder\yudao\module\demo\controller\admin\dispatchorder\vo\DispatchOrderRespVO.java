package cn.iocoder.yudao.module.demo.controller.admin.dispatchorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

import cn.iocoder.yudao.module.demo.controller.admin.materialacceptdetail.vo.MaterialAcceptDetailRespVO;

@Schema(description = "管理后台 - 调令信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DispatchOrderRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30182")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单号")
    private String orderNo;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("类型")
    private String type;

    @Schema(description = "被调单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("被调单位")
    private Long targetUnit;

    @Schema(description = "目的地", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("目的地")
    private String destination;

    @Schema(description = "部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26166")
    @ExcelProperty("部门ID")
    private Long deptId;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("状态")
    private Integer status;

    @Schema(description = "是否签收", example = "true")
    @ExcelProperty("是否签收")
    private Boolean isSign;

    @Schema(description = "签收时间")
    @ExcelProperty("签收时间")
    private LocalDateTime signTime;

    @Schema(description = "签收人员ID")
    @ExcelProperty("签收人员ID")
    private Long signPersonId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "物资详情集合")
    private List<MaterialAcceptDetailRespVO> materialDetails;

}