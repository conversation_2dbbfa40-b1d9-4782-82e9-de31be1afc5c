package cn.iocoder.yudao.module.demo.controller.admin.hkvideos;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/demo/hkVideos")
public class HKVideoController {

    /**
     * API网关的后端服务上下文为：/artemis
     */
    private static final String ARTEMIS_PATH = "/artemis";

//    private static final String CAMS_API = ARTEMIS_PATH + "/api/resource/v2/camera/search";
    /**
     * 分页获取监控点资源
     */
    private static final String CAMS_API = ARTEMIS_PATH + "/api/resource/v1/cameras";

    /**
     * 获取监控点预览取流
     */
    private static final String PREVIEW_API = ARTEMIS_PATH + "/api/video/v2/cameras/previewURLs";

    /**
     * 根据编号获取区域详细信息
     */
    private static final String REGION_INFO_API = ARTEMIS_PATH + "/api/resource/v1/region/regionCatalog/regionInfo";

    /**
     * 平台参数
     */
    private static final String HOST = "**************:443";
    private static final String APP_KEY = "24585743";
    private static final String APP_SECRET = "S12sgpVIj3s9LwerA6Xs";

    @GetMapping("/cameraList")
    public List<Map> getCameraList() throws Exception {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(HOST);
        config.setAppKey(APP_KEY);
        config.setAppSecret(APP_SECRET);

        Map<String, Object> paramMap = new HashMap<String, Object>();// post请求Form表单参数
        paramMap.put("pageNo", 1);
        paramMap.put("pageSize", 10);
        String body = JSON.toJSON(paramMap).toString();

        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", CAMS_API);
            }
        };
        List<Map> previewList = new ArrayList<>();

        JSONObject result = JSON.parseObject(ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json"));
        List<Map> cameraList = (List<Map>) ((JSONObject) result.get("data")).get("list");
        for (Map camera : cameraList) {
            Map<String, String> map = new HashMap<>();
            List<String> regionIndexCodes = new ArrayList<>();
            String cameraIndexCode = (String) camera.get("cameraIndexCode");
            String cameraName = (String) camera.get("cameraName");
            String regionIndexCode = (String) camera.get("regionIndexCode");
            regionIndexCodes.add(regionIndexCode);
            String previewURL = getPreviewURL(cameraIndexCode);
            String regionName = getRegionName(regionIndexCodes);
            map.put("cameraName", cameraName);
            map.put("previewURL", previewURL);
            map.put("regionName", regionName);
            previewList.add(map);
        }
        return previewList;
    }

    /**
     * 获取摄像头的预览流地址（hls流）
     * cameraIndexCode: 监控点唯一标识
     */
    private String getPreviewURL(String cameraIndexCode) throws Exception {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(HOST);
        config.setAppKey(APP_KEY);
        config.setAppSecret(APP_SECRET);

        Map<String, Object> paramMap = new HashMap<String, Object>();// post请求Form表单参数
        paramMap.put("cameraIndexCode", cameraIndexCode);
        paramMap.put("streamType", 0);
        paramMap.put("protocol", "hls");
        paramMap.put("transmode", 1);
        paramMap.put("expand", "streamform=ps");
        paramMap.put("streamform", "ps");
        String body = JSON.toJSON(paramMap).toString();

        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", PREVIEW_API);
            }
        };

        JSONObject result = JSON.parseObject(ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json"));
        String url = (String) ((JSONObject) result.get("data")).get("url");

        return url;
    }

    /**
     * 根据编号获取区域名称
     * indexCodes: 区域编号
     */
    private String getRegionName(List<String> indexCodes) throws Exception {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(HOST);
        config.setAppKey(APP_KEY);
        config.setAppSecret(APP_SECRET);

        Map<String, Object> paramMap = new HashMap<String, Object>();// post请求Form表单参数
        paramMap.put("indexCodes", indexCodes);
        String body = JSON.toJSON(paramMap).toString();

        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", REGION_INFO_API);
            }
        };

        JSONObject result = JSON.parseObject(ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json"));
        String regionName = (String) ((List<Map>) ((JSONObject) result.get("data")).get("list")).get(0).get("regionName");
        return regionName;
    }
}
