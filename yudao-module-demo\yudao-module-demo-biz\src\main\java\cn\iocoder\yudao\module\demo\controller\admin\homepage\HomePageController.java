package cn.iocoder.yudao.module.demo.controller.admin.homepage;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.demo.controller.admin.bizflownode.vo.BizFlowNodeSaveReqVO;
import cn.iocoder.yudao.module.demo.controller.admin.homepage.vo.HomepageTopVO;
import cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo.MaterialAcceptWithFlowRespVO;
import cn.iocoder.yudao.module.demo.dal.dataobject.bizflownode.BizFlowNodeDO;
import cn.iocoder.yudao.module.demo.service.homepage.HomepageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 首页
 */
@Tag(name = "管理后台 - 首页")
@RestController
@RequestMapping("/demo/homepage")
@Validated
public class HomePageController {
    @Resource
    private HomepageService homepageService;

    /**
     * 头部指标
     * @return
     */
    @GetMapping("/top")
    @Operation(summary = "头部指标")
    public CommonResult<HomepageTopVO> getTop() {
        HomepageTopVO topVO = homepageService.getTop();

        return success(topVO);
    }

    /**
     * 待审工单
     * @return
     */
    @GetMapping("/needApprove")
    @Operation(summary = "头部指标")
    public CommonResult<List<MaterialAcceptWithFlowRespVO>> getNeedApprove() {
        List<MaterialAcceptWithFlowRespVO> resultList = homepageService.getNeedApprove();

        return success(resultList);
    }

}
