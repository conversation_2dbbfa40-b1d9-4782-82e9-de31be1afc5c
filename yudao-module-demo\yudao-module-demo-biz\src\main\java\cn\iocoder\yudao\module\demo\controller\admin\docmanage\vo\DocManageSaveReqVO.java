package cn.iocoder.yudao.module.demo.controller.admin.docmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 文档管理新增/修改 Request VO")
@Data
public class DocManageSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23818")
    private Long id;

    @Schema(description = "文档分类", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "文档分类不能为空")
    private String docCategory;

    @Schema(description = "文档描述")
    private String docDesc;

    @Schema(description = "关键词，逗号分隔", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "关键词，逗号分隔不能为空")
    private String keywords;

    @Schema(description = "文档所属（1=个人文档，2=公共文档，3=收藏文档）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "文档所属（1=个人文档，2=公共文档，3=收藏文档）不能为空")
    private Integer docBelong;

    @Schema(description = "文件id", example = "12957")
    private String fileId;

}