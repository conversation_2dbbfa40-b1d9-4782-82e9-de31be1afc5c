package cn.iocoder.yudao.module.demo.controller.admin.docmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
public class DocManagePageItemVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "文档名称")
    private String docName;

    @Schema(description = "文档类型")
    private String docType;

    @Schema(description = "文档分类")
    private String docCategory;

    @Schema(description = "文档大小")
    private String docSize;

    @Schema(description = "文档描述")
    private String docDesc;

    @Schema(description = "关键词")
    private String keywords;

    @Schema(description = "文件ID")
    private Long fileId;

    @Schema(description = "文档所属")
    private Integer docBelong;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private String updater;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除")
    private Boolean deleted;

    // ========== infra_file 字段 ==========
    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "文件URL")
    private String fileUrl;

    @Schema(description = "文件大小")
    private Long fileSize;

    @Schema(description = "文件类型")
    private String fileType;
}
