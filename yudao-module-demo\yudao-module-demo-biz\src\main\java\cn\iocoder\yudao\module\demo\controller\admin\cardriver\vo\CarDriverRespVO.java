package cn.iocoder.yudao.module.demo.controller.admin.cardriver.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 驾驶员 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CarDriverRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25588")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "驾驶员姓名", example = "李四")
    @ExcelProperty("驾驶员姓名")
    private String driverName;

    @Schema(description = "联系电话")
    @ExcelProperty("联系电话")
    private String phone;

    @Schema(description = "驾驶证号")
    @ExcelProperty("驾驶证号")
    private String licenseNumber;

    @Schema(description = "准驾车型", example = "2")
    @ExcelProperty("准驾车型")
    private String licenseType;

    @Schema(description = "驾驶证照片")
    @ExcelProperty("驾驶证照片")
    private String licensePhoto;

    @Schema(description = "行驶证号")
    @ExcelProperty("行驶证号")
    private String drivingLicenseNumber;

    @Schema(description = "行驶证照片")
    @ExcelProperty("行驶证照片")
    private String drivingLicensePhoto;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}