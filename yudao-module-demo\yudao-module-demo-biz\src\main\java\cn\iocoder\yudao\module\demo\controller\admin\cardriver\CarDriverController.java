package cn.iocoder.yudao.module.demo.controller.admin.cardriver;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.demo.controller.admin.cardriver.vo.*;
import cn.iocoder.yudao.module.demo.dal.dataobject.cardriver.CarDriverDO;
import cn.iocoder.yudao.module.demo.service.cardriver.CarDriverService;

@Tag(name = "管理后台 - 驾驶员")
@RestController
@RequestMapping("/demo/car-driver")
@Validated
public class CarDriverController {

    @Resource
    private CarDriverService carDriverService;

    @PostMapping("/create")
    @Operation(summary = "创建驾驶员")
    @PreAuthorize("@ss.hasPermission('demo:car-driver:create')")
    public CommonResult<Long> createCarDriver(@Valid @RequestBody CarDriverSaveReqVO createReqVO) {
        return success(carDriverService.createCarDriver(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新驾驶员")
    @PreAuthorize("@ss.hasPermission('demo:car-driver:update')")
    public CommonResult<Boolean> updateCarDriver(@Valid @RequestBody CarDriverSaveReqVO updateReqVO) {
        carDriverService.updateCarDriver(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除驾驶员")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('demo:car-driver:delete')")
    public CommonResult<Boolean> deleteCarDriver(@RequestParam("id") Long id) {
        carDriverService.deleteCarDriver(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得驾驶员")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('demo:car-driver:query')")
    public CommonResult<CarDriverRespVO> getCarDriver(@RequestParam("id") Long id) {
        CarDriverDO carDriver = carDriverService.getCarDriver(id);
        return success(BeanUtils.toBean(carDriver, CarDriverRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得驾驶员分页")
    @PreAuthorize("@ss.hasPermission('demo:car-driver:queryPage')")
    public CommonResult<PageResult<CarDriverRespVO>> getCarDriverPage(@Valid CarDriverPageReqVO pageReqVO) {
        PageResult<CarDriverDO> pageResult = carDriverService.getCarDriverPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CarDriverRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出驾驶员 Excel")
    @PreAuthorize("@ss.hasPermission('demo:car-driver:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCarDriverExcel(@Valid CarDriverPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CarDriverDO> list = carDriverService.getCarDriverPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "驾驶员.xls", "数据", CarDriverRespVO.class,
                        BeanUtils.toBean(list, CarDriverRespVO.class));
    }

}