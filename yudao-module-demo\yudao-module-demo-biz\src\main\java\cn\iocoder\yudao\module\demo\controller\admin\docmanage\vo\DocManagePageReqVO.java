package cn.iocoder.yudao.module.demo.controller.admin.docmanage.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 文档管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DocManagePageReqVO extends PageParam {

    @Schema(description = "文件名（模糊匹配infra_file.name）")
    private String fileName;

    @Schema(description = "关键词（模糊匹配t_doc_manage.keywords）")
    private String keywords;

    @Schema(description = "开始日期（t_doc_manage.create_time）")
    private LocalDateTime beginCreateTime;

    @Schema(description = "结束日期（t_doc_manage.create_time）")
    private LocalDateTime endCreateTime;

}