package cn.iocoder.yudao.module.demo.controller.admin.checkstandard;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.demo.controller.admin.checkstandard.vo.*;
import cn.iocoder.yudao.module.demo.dal.dataobject.checkstandard.MaterialCheckStandardDO;
import cn.iocoder.yudao.module.demo.service.checkstandard.MaterialCheckStandardService;

@Tag(name = "管理后台 - 巡检项目")
@RestController
@RequestMapping("/demo/material-check-standard")
@Validated
public class MaterialCheckStandardController {

    @Resource
    private MaterialCheckStandardService materialCheckStandardService;

    @PostMapping("/create")
    @Operation(summary = "创建巡检项目")
    @PreAuthorize("@ss.hasPermission('demo:material-check-standard:create')")
    public CommonResult<Long> createMaterialCheckStandard(@Valid @RequestBody MaterialCheckStandardSaveReqVO createReqVO) {
        return success(materialCheckStandardService.createMaterialCheckStandard(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新巡检项目")
    @PreAuthorize("@ss.hasPermission('demo:material-check-standard:update')")
    public CommonResult<Boolean> updateMaterialCheckStandard(@Valid @RequestBody MaterialCheckStandardSaveReqVO updateReqVO) {
        materialCheckStandardService.updateMaterialCheckStandard(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除巡检项目")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('demo:material-check-standard:delete')")
    public CommonResult<Boolean> deleteMaterialCheckStandard(@RequestParam("id") Long id) {
        materialCheckStandardService.deleteMaterialCheckStandard(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得巡检项目")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('demo:material-check-standard:query')")
    public CommonResult<MaterialCheckStandardRespVO> getMaterialCheckStandard(@RequestParam("id") Long id) {
        MaterialCheckStandardDO materialCheckStandard = materialCheckStandardService.getMaterialCheckStandard(id);
        return success(BeanUtils.toBean(materialCheckStandard, MaterialCheckStandardRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得巡检项目分页")
    @PreAuthorize("@ss.hasPermission('demo:material-check-standard:query')")
    public CommonResult<PageResult<MaterialCheckStandardRespVO>> getMaterialCheckStandardPage(@Valid MaterialCheckStandardPageReqVO pageReqVO) {
        PageResult<MaterialCheckStandardDO> pageResult = materialCheckStandardService.getMaterialCheckStandardPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MaterialCheckStandardRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出巡检项目 Excel")
    @PreAuthorize("@ss.hasPermission('demo:material-check-standard:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMaterialCheckStandardExcel(@Valid MaterialCheckStandardPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MaterialCheckStandardDO> list = materialCheckStandardService.getMaterialCheckStandardPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "巡检项目.xls", "数据", MaterialCheckStandardRespVO.class,
                        BeanUtils.toBean(list, MaterialCheckStandardRespVO.class));
    }

}