package cn.iocoder.yudao.module.demo.controller.admin.carequipment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 车辆挂载终端新增/修改 Request VO")
@Data
public class CarEquipmentSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28782")
    private Long id;

    @Schema(description = "设备名称", example = "芋艿")
    private String equipmentName;

    @Schema(description = "型号")
    private String modelxh;

    @Schema(description = "设备照片")
    private String equipmentPhoto;

    @Schema(description = "状态", example = "2")
    private String status;

    @Schema(description = "所在单位ID")
    private BigDecimal unitId;

    @Schema(description = "所在单位名称")
    private String unitName;

}