package cn.iocoder.yudao.module.demo.controller.admin.carequipment;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.demo.controller.admin.carequipment.vo.*;
import cn.iocoder.yudao.module.demo.dal.dataobject.carequipment.CarEquipmentDO;
import cn.iocoder.yudao.module.demo.service.carequipment.CarEquipmentService;

@Tag(name = "管理后台 - 车辆挂载终端")
@RestController
@RequestMapping("/demo/car-equipment")
@Validated
public class CarEquipmentController {

    @Resource
    private CarEquipmentService carEquipmentService;

    @PostMapping("/create")
    @Operation(summary = "创建车辆挂载终端")
    @PreAuthorize("@ss.hasPermission('demo:car-equipment:create')")
    public CommonResult<Long> createCarEquipment(@Valid @RequestBody CarEquipmentSaveReqVO createReqVO) {
        return success(carEquipmentService.createCarEquipment(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新车辆挂载终端")
    @PreAuthorize("@ss.hasPermission('demo:car-equipment:update')")
    public CommonResult<Boolean> updateCarEquipment(@Valid @RequestBody CarEquipmentSaveReqVO updateReqVO) {
        carEquipmentService.updateCarEquipment(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除车辆挂载终端")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('demo:car-equipment:delete')")
    public CommonResult<Boolean> deleteCarEquipment(@RequestParam("id") Long id) {
        carEquipmentService.deleteCarEquipment(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得车辆挂载终端")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('demo:car-equipment:query')")
    public CommonResult<CarEquipmentRespVO> getCarEquipment(@RequestParam("id") Long id) {
        CarEquipmentDO carEquipment = carEquipmentService.getCarEquipment(id);
        return success(BeanUtils.toBean(carEquipment, CarEquipmentRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得车辆挂载终端分页")
    @PreAuthorize("@ss.hasPermission('demo:car-equipment:queryPage')")
    public CommonResult<PageResult<CarEquipmentRespVO>> getCarEquipmentPage(@Valid CarEquipmentPageReqVO pageReqVO) {
        PageResult<CarEquipmentDO> pageResult = carEquipmentService.getCarEquipmentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CarEquipmentRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出车辆挂载终端 Excel")
    @PreAuthorize("@ss.hasPermission('demo:car-equipment:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCarEquipmentExcel(@Valid CarEquipmentPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CarEquipmentDO> list = carEquipmentService.getCarEquipmentPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "车辆挂载终端.xls", "数据", CarEquipmentRespVO.class,
                        BeanUtils.toBean(list, CarEquipmentRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得所有车辆挂载终端")
    public CommonResult<List<CarEquipmentRespVO>> getCarEquipmentList() {
        List<CarEquipmentDO> list = carEquipmentService.getCarEquipmentList();
        return success(BeanUtils.toBean(list, CarEquipmentRespVO.class));
    }

}