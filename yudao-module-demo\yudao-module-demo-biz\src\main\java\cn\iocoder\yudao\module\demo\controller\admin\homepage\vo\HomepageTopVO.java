package cn.iocoder.yudao.module.demo.controller.admin.homepage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class HomepageTopVO {

    @Schema(description = "物资总数", example = "1")
    private Long materialCount = 0L;

    @Schema(description = "仓库数量", example = "2")
    private Long warehouseCount = 0L;

    @Schema(description = "预警数量", example = "3")
    private Long warningCount = 0L;

    @Schema(description = "到期提醒", example = "4")
    private Long remindCount = 0L;

}
