package cn.iocoder.yudao.module.demo.controller.admin.carunit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 车辆协同单位新增/修改 Request VO")
@Data
public class CarUnitSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "87")
    private Long id;

    @Schema(description = "单位名称", example = "李四")
    private String unitName;

    @Schema(description = "单位类型", example = "2")
    private String unitType;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "统一社会信用代码")
    private String socialCreditCode;

    @Schema(description = "注册地址")
    private String registrationAddress;

    @Schema(description = "合同期限开始日期")
    private LocalDateTime contractPeriodStart;

    @Schema(description = "合同期限结束日期")
    private LocalDateTime contractPeriodEnd;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "营业执照")
    private String businessLicense;

    @Schema(description = "合作状态")
    private String cooperStatus;

    @Schema(description = "所在单位ID")
    private BigDecimal deptId;

    @Schema(description = "所在单位名称")
    private String deptName;
}