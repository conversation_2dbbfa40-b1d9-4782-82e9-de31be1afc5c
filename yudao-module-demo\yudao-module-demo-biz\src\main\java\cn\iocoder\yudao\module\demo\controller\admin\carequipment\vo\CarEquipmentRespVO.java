package cn.iocoder.yudao.module.demo.controller.admin.carequipment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 车辆挂载终端 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CarEquipmentRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28782")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "设备名称", example = "芋艿")
    @ExcelProperty("设备名称")
    private String equipmentName;

    @Schema(description = "型号")
    @ExcelProperty("型号")
    private String modelxh;

    @Schema(description = "设备照片")
    @ExcelProperty("设备照片")
    private String equipmentPhoto;

    @Schema(description = "状态", example = "2")
    @ExcelProperty("状态")
    private String status;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "所在单位ID")
    private BigDecimal unitId;

    @Schema(description = "所在单位名称")
    private String unitName;

}