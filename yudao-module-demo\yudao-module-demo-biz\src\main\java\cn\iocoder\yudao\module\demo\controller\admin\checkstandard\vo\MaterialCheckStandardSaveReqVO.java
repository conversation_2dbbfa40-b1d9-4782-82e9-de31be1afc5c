package cn.iocoder.yudao.module.demo.controller.admin.checkstandard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 巡检项目新增/修改 Request VO")
@Data
public class MaterialCheckStandardSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12614")
    private Long id;

    @Schema(description = "关联业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32018")
    private Long acceptId;

    @Schema(description = "检查项目")
    private String checkItem;

    @Schema(description = "检查标准")
    private String checkStandard;

    @Schema(description = "巡检结果")
    private String checkResult;

}
