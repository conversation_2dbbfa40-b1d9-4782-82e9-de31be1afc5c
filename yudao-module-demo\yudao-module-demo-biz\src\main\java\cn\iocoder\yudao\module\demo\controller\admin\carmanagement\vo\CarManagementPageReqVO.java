package cn.iocoder.yudao.module.demo.controller.admin.carmanagement.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 车辆管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CarManagementPageReqVO extends PageParam {

    @Schema(description = "车牌号")
    private String vehicleNumber;

    @Schema(description = "车辆类型", example = "1")
    private String vehicleType;

    @Schema(description = "协同单位ID", example = "21847")
    private Long cooperativeId;

    @Schema(description = "协同单位名称", example = "李四")
    private String cooperativeName;

    @Schema(description = "载重能力")
    private BigDecimal loadCapacity;

    @Schema(description = "车辆长度")
    private BigDecimal length;

    @Schema(description = "车辆宽度")
    private BigDecimal width;

    @Schema(description = "车辆高度")
    private BigDecimal height;

    @Schema(description = "车辆照片")
    private String vehiclePhoto;

    @Schema(description = "车辆状态", example = "1")
    private String vehicleStatus;

    @Schema(description = "关联驾驶员")
    private String contactDriver;

    @Schema(description = "关联驾驶员ID")
    private String contactDriverId;

    @Schema(description = "布控球类型", example = "1")
    private String airbagType;

    @Schema(description = "布控球照片")
    private String airbagPhoto;

    @Schema(description = "记录仪类型", example = "2")
    private String recorderType;

    @Schema(description = "记录仪照片")
    private String recorderPhoto;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "所在单位ID")
    private BigDecimal unitId;

    @Schema(description = "所在单位名称")
    private String unitName;
}