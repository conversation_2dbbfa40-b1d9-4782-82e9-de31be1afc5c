package cn.iocoder.yudao.module.demo.controller.admin.carmanagement.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 车辆管理新增/修改 Request VO")
@Data
public class CarManagementSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "7836")
    private Long id;

    @Schema(description = "车牌号")
    private String vehicleNumber;

    @Schema(description = "车辆类型", example = "1")
    private String vehicleType;

    @Schema(description = "协同单位ID", example = "21847")
    private Long cooperativeId;

    @Schema(description = "协同单位名称", example = "李四")
    private String cooperativeName;

    @Schema(description = "载重能力")
    private BigDecimal loadCapacity;

    @Schema(description = "车辆长度")
    private BigDecimal length;

    @Schema(description = "车辆宽度")
    private BigDecimal width;

    @Schema(description = "车辆高度")
    private BigDecimal height;

    @Schema(description = "车辆照片")
    private String vehiclePhoto;

    @Schema(description = "车辆状态", example = "1")
    private String vehicleStatus;

    @Schema(description = "关联驾驶员")
    private String contactDriver;

    @Schema(description = "关联驾驶员ID")
    private String contactDriverId;

    @Schema(description = "布控球类型", example = "1")
    private String airbagType;

    @Schema(description = "布控球照片")
    private String airbagPhoto;

    @Schema(description = "记录仪类型", example = "2")
    private String recorderType;

    @Schema(description = "记录仪照片")
    private String recorderPhoto;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "所在单位ID")
    private BigDecimal unitId;

    @Schema(description = "所在单位名称")
    private String unitName;

}