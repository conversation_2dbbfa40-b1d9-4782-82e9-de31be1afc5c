package cn.iocoder.yudao.module.demo.controller.admin.docmanage;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.demo.controller.admin.docmanage.vo.*;
import cn.iocoder.yudao.module.demo.dal.dataobject.docmanage.DocManageDO;
import cn.iocoder.yudao.module.demo.service.docmanage.DocManageService;

@Tag(name = "管理后台 - 文档管理")
@RestController
@RequestMapping("/demo/doc-manage")
@Validated
public class DocManageController {

    @Resource
    private DocManageService docManageService;

    @PostMapping("/create")
    @Operation(summary = "创建文档管理")
    @PreAuthorize("@ss.hasPermission('demo:doc-manage:create')")
    public CommonResult<Long> createDocManage(@Valid @RequestBody DocManageSaveReqVO createReqVO) {
        return success(docManageService.createDocManage(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新文档管理")
    @PreAuthorize("@ss.hasPermission('demo:doc-manage:update')")
    public CommonResult<Boolean> updateDocManage(@Valid @RequestBody DocManageSaveReqVO updateReqVO) {
        docManageService.updateDocManage(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除文档管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('demo:doc-manage:delete')")
    public CommonResult<Boolean> deleteDocManage(@RequestParam("id") Long id) {
        docManageService.deleteDocManage(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得文档管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('demo:doc-manage:query')")
    public CommonResult<DocManageRespVO> getDocManage(@RequestParam("id") Long id) {
        DocManageDO docManage = docManageService.getDocManage(id);
        return success(BeanUtils.toBean(docManage, DocManageRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得文档管理分页")
    @PreAuthorize("@ss.hasPermission('demo:doc-manage:query')")
    public CommonResult<PageResult<DocManagePageItemVO>> getDocManagePage(@Valid DocManagePageReqVO pageReqVO) {
        return success(docManageService.getDocManagePage(pageReqVO));
    }


}
