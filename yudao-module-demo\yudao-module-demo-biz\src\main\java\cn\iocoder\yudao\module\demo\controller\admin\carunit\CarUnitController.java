package cn.iocoder.yudao.module.demo.controller.admin.carunit;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.demo.controller.admin.carunit.vo.*;
import cn.iocoder.yudao.module.demo.dal.dataobject.carunit.CarUnitDO;
import cn.iocoder.yudao.module.demo.service.carunit.CarUnitService;

@Tag(name = "管理后台 - 车辆协同单位")
@RestController
@RequestMapping("/demo/car-unit")
@Validated
public class CarUnitController {

    @Resource
    private CarUnitService carUnitService;

    @PostMapping("/create")
    @Operation(summary = "创建车辆协同单位")
    @PreAuthorize("@ss.hasPermission('demo:car-unit:create')")
    public CommonResult<Long> createCarUnit(@Valid @RequestBody CarUnitSaveReqVO createReqVO) {
        return success(carUnitService.createCarUnit(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新车辆协同单位")
    @PreAuthorize("@ss.hasPermission('demo:car-unit:update')")
    public CommonResult<Boolean> updateCarUnit(@Valid @RequestBody CarUnitSaveReqVO updateReqVO) {
        carUnitService.updateCarUnit(updateReqVO);
        return success(true);
    }

    @GetMapping("/terminal")
    @Operation(summary = "终止协同单位合作")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('demo:car-unit:terminal')")
    public CommonResult<Boolean> terminalCarUnit(@RequestParam("id") Long id) {
        carUnitService.terminalCooper(id);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除车辆协同单位")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('demo:car-unit:delete')")
    public CommonResult<Boolean> deleteCarUnit(@RequestParam("id") Long id) {
        carUnitService.deleteCarUnit(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得车辆协同单位")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('demo:car-unit:query')")
    public CommonResult<CarUnitRespVO> getCarUnit(@RequestParam("id") Long id) {
        CarUnitDO carUnit = carUnitService.getCarUnit(id);
        return success(BeanUtils.toBean(carUnit, CarUnitRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得车辆协同单位分页")
    @PreAuthorize("@ss.hasPermission('demo:car-unit:query')")
    public CommonResult<PageResult<CarUnitRespVO>> getCarUnitPage(@Valid CarUnitPageReqVO pageReqVO) {
        PageResult<CarUnitDO> pageResult = carUnitService.getCarUnitPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CarUnitRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出车辆协同单位 Excel")
    @PreAuthorize("@ss.hasPermission('demo:car-unit:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCarUnitExcel(@Valid CarUnitPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CarUnitDO> list = carUnitService.getCarUnitPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "车辆协同单位.xls", "数据", CarUnitRespVO.class,
                        BeanUtils.toBean(list, CarUnitRespVO.class));
    }

}