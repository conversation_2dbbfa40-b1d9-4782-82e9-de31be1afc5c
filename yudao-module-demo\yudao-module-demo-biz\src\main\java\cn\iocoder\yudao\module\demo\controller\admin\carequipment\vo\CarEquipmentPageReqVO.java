package cn.iocoder.yudao.module.demo.controller.admin.carequipment.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 车辆挂载终端分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CarEquipmentPageReqVO extends PageParam {

    @Schema(description = "设备名称", example = "芋艿")
    private String equipmentName;

    @Schema(description = "型号")
    private String modelxh;

    @Schema(description = "设备照片")
    private String equipmentPhoto;

    @Schema(description = "状态", example = "2")
    private String status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "所在单位ID")
    private BigDecimal unitId;

    @Schema(description = "所在单位名称")
    private String unitName;

}