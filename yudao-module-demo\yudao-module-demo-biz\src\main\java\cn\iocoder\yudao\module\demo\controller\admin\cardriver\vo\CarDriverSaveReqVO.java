package cn.iocoder.yudao.module.demo.controller.admin.cardriver.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 驾驶员新增/修改 Request VO")
@Data
public class CarDriverSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25588")
    private Long id;

    @Schema(description = "驾驶员姓名", example = "李四")
    private String driverName;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "驾驶证号")
    private String licenseNumber;

    @Schema(description = "准驾车型", example = "2")
    private String licenseType;

    @Schema(description = "驾驶证照片")
    private String licensePhoto;

    @Schema(description = "行驶证号")
    private String drivingLicenseNumber;

    @Schema(description = "行驶证照片")
    private String drivingLicensePhoto;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}