package cn.iocoder.yudao.module.demo.controller.admin.cardriver.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 驾驶员分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CarDriverPageReqVO extends PageParam {

    @Schema(description = "驾驶员姓名", example = "李四")
    private String driverName;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "驾驶证号")
    private String licenseNumber;

    @Schema(description = "准驾车型", example = "2")
    private String licenseType;

    @Schema(description = "驾驶证照片")
    private String licensePhoto;

    @Schema(description = "行驶证号")
    private String drivingLicenseNumber;

    @Schema(description = "行驶证照片")
    private String drivingLicensePhoto;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}