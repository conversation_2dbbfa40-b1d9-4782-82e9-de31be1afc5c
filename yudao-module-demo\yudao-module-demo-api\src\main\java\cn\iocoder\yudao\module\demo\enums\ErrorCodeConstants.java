package cn.iocoder.yudao.module.demo.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * Bpm 错误码枚举类
 * <p>
 * bpm 系统，使用 1-009-000-000 段
 */
public interface ErrorCodeConstants {

    // ==========  通用流程处理 模块 1-009-000-000 ==========

    ErrorCode PRINT_NOT_EXISTS = new ErrorCode(1_009_001_001, "请假申请不存在");

    ErrorCode DOCUMENT_CATEGORY_NOT_EXISTS = new ErrorCode(1_009_001_002, "文档分类不存在");
    ErrorCode DOCUMENT_CATEGORY_EXITS_CHILDREN = new ErrorCode(1_009_001_003, "存在存在子文档分类，无法删除");
    ErrorCode DOCUMENT_CATEGORY_PARENT_NOT_EXITS = new ErrorCode(1_009_001_004, "父级文档分类不存在");
    ErrorCode DOCUMENT_CATEGORY_PARENT_ERROR = new ErrorCode(1_009_001_005, "不能设置自己为父文档分类");
    ErrorCode DOCUMENT_CATEGORY_CATEGORY_NAME_DUPLICATE = new ErrorCode(1_009_001_006, "已经存在该分类名称的文档分类");
    ErrorCode DOCUMENT_CATEGORY_PARENT_IS_CHILD = new ErrorCode(1_009_001_007, "不能设置自己的子DocumentCategory为父DocumentCategory");
    ErrorCode DOC_MANAGE_NOT_EXISTS = new ErrorCode(1_009_001_007, "文档不存在");
    ErrorCode BIZ_FLOW_NODE_NOT_EXISTS = new ErrorCode(1_009_001_008, "系统异常");

    ErrorCode WAREHOUSE_NOT_EXISTS = new ErrorCode(1_009_001_009, "仓库信息不存在");
    ErrorCode MATERIAL_ACCEPT_NOT_EXISTS = new ErrorCode(1_009_001_010, "物资验收信息不存在");
    ErrorCode MATERIAL_ACCEPT_DETAIL_NOT_EXISTS = new ErrorCode(1_009_001_011, "物资验收详情不存在");
    ErrorCode MATERIAL_MANAGEMENT_NOT_EXISTS = new ErrorCode(1_009_001_012, "物资管理不存在");
    ErrorCode MATERIAL_CATEGORY_NOT_EXISTS = new ErrorCode(1_009_001_013, "物资分类不存在");
    ErrorCode MATERIAL_DIRECTORY_NOT_EXISTS = new ErrorCode(1_009_001_014, "物资目录不存在");
    ErrorCode DISPATCH_ORDER_NOT_EXISTS = new ErrorCode(2_009_001_001, "调令信息不存在");
    ErrorCode MATERIAL_SUPPLIER_NOT_EXISTS = new ErrorCode(1_009_001_015, "物资厂商不存在");
    ErrorCode MATERIAL_INTRODUCTION_NOT_EXISTS = new ErrorCode(1_009_001_016, "物资介绍不存在");
    ErrorCode CAR_UNIT_NOT_EXISTS = new ErrorCode(1_009_001_017, "车辆协同单位不存在");
    ErrorCode CAR_EQUIPMENT_NOT_EXISTS = new ErrorCode(1_009_001_018, "车辆挂载终端不存在");
    ErrorCode CAR_DRIVER_NOT_EXISTS = new ErrorCode(1_009_001_019, "驾驶员不存在");
    ErrorCode CAR_MANAGEMENT_NOT_EXISTS = new ErrorCode(1_009_001_020, "车辆管理不存在");
    ErrorCode MATERIAL_ACCEPT_QUANTITY_ERROR = new ErrorCode(1_009_001_021, "修改后入库物资数量与验收物资数量不一致");
    ErrorCode IOT_DATA_NOT_EXISTS = new ErrorCode(1_009_001_022, "物联平台网关数据不存在");
    ErrorCode IOT_DEVICES_NOT_EXISTS = new ErrorCode(1_009_001_023, "物联设备不存在");
    ErrorCode MATERIAL_CHECK_STANDARD_NOT_EXISTS = new ErrorCode(1_009_001_024, "巡检项目不存在");
    ErrorCode IOT_WARNING_NOT_EXISTS = new ErrorCode(1_009_001_025, "物联平台设备告警数据不存在");
    ErrorCode INBOUND_ALLOCATION_RULE_NOT_EXISTS = new ErrorCode(2_009_001_025, "入库分配规则不存在");
    ErrorCode OUTBOUND_ALLOCATION_RULE_NOT_EXISTS = new ErrorCode(1_009_001_026, "出库分配规则不存在");
    ErrorCode INVENTORY_RULE_NOT_EXISTS = new ErrorCode(1_009_001_027, "出库分配规则不存在");
}
