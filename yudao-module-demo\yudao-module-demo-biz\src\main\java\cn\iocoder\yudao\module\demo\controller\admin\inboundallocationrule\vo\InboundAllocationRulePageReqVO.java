package cn.iocoder.yudao.module.demo.controller.admin.inboundallocationrule.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 入库分配规则分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InboundAllocationRulePageReqVO extends PageParam {

    @Schema(description = "规则名称", example = "赵六")
    private String ruleName;

    @Schema(description = "规则编码")
    private String ruleCode;

    @Schema(description = "规则类型", example = "1")
    private String ruleType;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "规则状态：1-启用，0-禁用", example = "1")
    private Integer ruleStatus;

    @Schema(description = "分类")
    private String category;

    @Schema(description = "名称", example = "王五")
    private String name;

    @Schema(description = "仓库名称", example = "李四")
    private String warehouseName;

    @Schema(description = "垛位名称", example = "芋艿")
    private String locationName;

    @Schema(description = "仓库ID", example = "19578")
    private Long warehouseId;

    @Schema(description = "垛位ID", example = "24409")
    private Long locationId;

    @Schema(description = "创建部门ID", example = "1764")
    private Long createDeptId;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}