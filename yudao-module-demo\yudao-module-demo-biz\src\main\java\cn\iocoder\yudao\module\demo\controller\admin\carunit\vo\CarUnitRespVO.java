package cn.iocoder.yudao.module.demo.controller.admin.carunit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 车辆协同单位 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CarUnitRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "87")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "单位名称", example = "李四")
    @ExcelProperty("单位名称")
    private String unitName;

    @Schema(description = "单位类型", example = "2")
    @ExcelProperty("单位类型")
    private String unitType;

    @Schema(description = "联系人")
    @ExcelProperty("联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    @ExcelProperty("联系电话")
    private String contactPhone;

    @Schema(description = "统一社会信用代码")
    @ExcelProperty("统一社会信用代码")
    private String socialCreditCode;

    @Schema(description = "注册地址")
    @ExcelProperty("注册地址")
    private String registrationAddress;

    @Schema(description = "合同期限开始日期")
    @ExcelProperty("合同期限开始日期")
    private LocalDateTime contractPeriodStart;

    @Schema(description = "合同期限结束日期")
    @ExcelProperty("合同期限结束日期")
    private LocalDateTime contractPeriodEnd;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "营业执照")
    @ExcelProperty("营业执照")
    private String businessLicense;

    @Schema(description = "合作状态")
    @ExcelProperty("合作状态")
    private String cooperStatus;

    @Schema(description = "所在单位ID")
    private BigDecimal deptId;

    @Schema(description = "所在单位名称")
    @ExcelProperty("所在单位名称")
    private String deptName;
}