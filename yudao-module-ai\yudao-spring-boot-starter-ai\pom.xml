<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-module-ai</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yudao-spring-boot-starter-ai</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>AI 大模型拓展，接入国内外大模型</description>
    <properties>
        <spring-ai.version>1.0.0-M6</spring-ai.version>
        <tinyflow.version>1.0.0-rc.3</tinyflow.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-common</artifactId>
        </dependency>

        <!-- Spring AI Model 模型接入 -->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-openai-spring-boot-starter</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-azure-openai-spring-boot-starter</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-ollama-spring-boot-starter</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-stability-ai-spring-boot-starter</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <!-- 通义千问 -->
            <groupId>com.alibaba.cloud.ai</groupId>
            <artifactId>spring-ai-alibaba-starter</artifactId>
            <version>${spring-ai.version}.1</version>
        </dependency>
        <dependency>
            <!-- 文心一言 -->
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-qianfan-spring-boot-starter</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <!-- 智谱 GLM -->
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-zhipuai-spring-boot-starter</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-minimax-spring-boot-starter</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-moonshot-spring-boot-starter</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>

        <!-- 向量存储：https://db-engines.com/en/ranking/vector+dbms -->
        <dependency>
            <!-- Qdrant：https://qdrant.tech/ -->
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-qdrant-store</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>

        <dependency>
            <!-- Redis：https://redis.io/docs/latest/develop/get-started/vector-database/ -->
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-redis-store</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-redis</artifactId>
        </dependency>

        <dependency>
            <!-- Milvus：https://milvus.io/ -->
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-milvus-store</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>

        <dependency>
            <!-- Tika：负责内容的解析 -->
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-tika-document-reader</artifactId>
            <version>${spring-ai.version}</version>
            <!-- TODO 芋艿：boot 项目里，不引入 cloud 依赖！！！另外，这样也是为了解决启动报错的问题！ -->
            <exclusions>
                <exclusion>
                    <artifactId>spring-cloud-function-context</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-cloud-function-core</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- TinyFlow：AI 工作流 -->
        <dependency>
            <groupId>dev.tinyflow</groupId>
            <artifactId>tinyflow-java-core</artifactId>
            <version>${tinyflow.version}</version>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>