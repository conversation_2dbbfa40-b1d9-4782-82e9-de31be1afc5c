package cn.iocoder.yudao.module.demo.controller.admin.inboundallocationrule;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.demo.controller.admin.inboundallocationrule.vo.*;
import cn.iocoder.yudao.module.demo.dal.dataobject.inboundallocationrule.InboundAllocationRuleDO;
import cn.iocoder.yudao.module.demo.service.inboundallocationrule.InboundAllocationRuleService;

@Tag(name = "管理后台 - 入库分配规则")
@RestController
@RequestMapping("/demo/inbound-allocation-rule")
@Validated
public class InboundAllocationRuleController {

    @Resource
    private InboundAllocationRuleService inboundAllocationRuleService;

    @PostMapping("/create")
    @Operation(summary = "创建入库分配规则")
    @PreAuthorize("@ss.hasPermission('demo:inbound-allocation-rule:create')")
    public CommonResult<Long> createInboundAllocationRule(@Valid @RequestBody InboundAllocationRuleSaveReqVO createReqVO) {
        return success(inboundAllocationRuleService.createInboundAllocationRule(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新入库分配规则")
    @PreAuthorize("@ss.hasPermission('demo:inbound-allocation-rule:update')")
    public CommonResult<Boolean> updateInboundAllocationRule(@Valid @RequestBody InboundAllocationRuleSaveReqVO updateReqVO) {
        inboundAllocationRuleService.updateInboundAllocationRule(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除入库分配规则")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('demo:inbound-allocation-rule:delete')")
    public CommonResult<Boolean> deleteInboundAllocationRule(@RequestParam("id") Long id) {
        inboundAllocationRuleService.deleteInboundAllocationRule(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得入库分配规则")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('demo:inbound-allocation-rule:query')")
    public CommonResult<InboundAllocationRuleRespVO> getInboundAllocationRule(@RequestParam("id") Long id) {
        InboundAllocationRuleDO inboundAllocationRule = inboundAllocationRuleService.getInboundAllocationRule(id);
        return success(BeanUtils.toBean(inboundAllocationRule, InboundAllocationRuleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得入库分配规则分页")
    @PreAuthorize("@ss.hasPermission('demo:inbound-allocation-rule:query')")
    public CommonResult<PageResult<InboundAllocationRuleRespVO>> getInboundAllocationRulePage(@Valid InboundAllocationRulePageReqVO pageReqVO) {
        PageResult<InboundAllocationRuleDO> pageResult = inboundAllocationRuleService.getInboundAllocationRulePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InboundAllocationRuleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出入库分配规则 Excel")
    @PreAuthorize("@ss.hasPermission('demo:inbound-allocation-rule:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInboundAllocationRuleExcel(@Valid InboundAllocationRulePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InboundAllocationRuleDO> list = inboundAllocationRuleService.getInboundAllocationRulePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "入库分配规则.xls", "数据", InboundAllocationRuleRespVO.class,
                        BeanUtils.toBean(list, InboundAllocationRuleRespVO.class));
    }

}
