package cn.iocoder.yudao.module.demo.controller.admin.carmanagement;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.demo.controller.admin.carmanagement.vo.*;
import cn.iocoder.yudao.module.demo.dal.dataobject.carmanagement.CarManagementDO;
import cn.iocoder.yudao.module.demo.service.carmanagement.CarManagementService;

@Tag(name = "管理后台 - 车辆管理")
@RestController
@RequestMapping("/demo/car-management")
@Validated
public class CarManagementController {

    @Resource
    private CarManagementService carManagementService;

    @PostMapping("/create")
    @Operation(summary = "创建车辆管理")
    @PreAuthorize("@ss.hasPermission('demo:car-management:create')")
    public CommonResult<Long> createCarManagement(@Valid @RequestBody CarManagementSaveReqVO createReqVO) {
        return success(carManagementService.createCarManagement(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新车辆管理")
    @PreAuthorize("@ss.hasPermission('demo:car-management:update')")
    public CommonResult<Boolean> updateCarManagement(@Valid @RequestBody CarManagementSaveReqVO updateReqVO) {
        carManagementService.updateCarManagement(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除车辆管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('demo:car-management:delete')")
    public CommonResult<Boolean> deleteCarManagement(@RequestParam("id") Long id) {
        carManagementService.deleteCarManagement(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得车辆管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('demo:car-management:query')")
    public CommonResult<CarManagementRespVO> getCarManagement(@RequestParam("id") Long id) {
        CarManagementDO carManagement = carManagementService.getCarManagement(id);
        return success(BeanUtils.toBean(carManagement, CarManagementRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得车辆管理分页")
    @PreAuthorize("@ss.hasPermission('demo:car-management:queryPage')")
    public CommonResult<PageResult<CarManagementRespVO>> getCarManagementPage(@Valid CarManagementPageReqVO pageReqVO) {
        PageResult<CarManagementDO> pageResult = carManagementService.getCarManagementPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CarManagementRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出车辆管理 Excel")
    @PreAuthorize("@ss.hasPermission('demo:car-management:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCarManagementExcel(@Valid CarManagementPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CarManagementDO> list = carManagementService.getCarManagementPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "车辆管理.xls", "数据", CarManagementRespVO.class,
                        BeanUtils.toBean(list, CarManagementRespVO.class));
    }

}