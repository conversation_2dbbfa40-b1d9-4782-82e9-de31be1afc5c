package cn.iocoder.yudao.module.demo.controller.admin.bizflownode;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.demo.controller.admin.bizflownode.vo.*;
import cn.iocoder.yudao.module.demo.dal.dataobject.bizflownode.BizFlowNodeDO;
import cn.iocoder.yudao.module.demo.service.bizflownode.BizFlowNodeService;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.demo.enums.ErrorCodeConstants;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

@Tag(name = "管理后台 - 业务流程节点配置")
@RestController
@RequestMapping("/demo/biz-flow-node")
@Validated
public class BizFlowNodeController {

    @Resource
    private BizFlowNodeService bizFlowNodeService;

    @PostMapping("/create")
    @Operation(summary = "创建业务流程节点配置")
    @PreAuthorize("@ss.hasPermission('demo:biz-flow-node:create')")
    public CommonResult<Long> createBizFlowNode(@Valid @RequestBody BizFlowNodeSaveReqVO createReqVO) {
        // 设置创建部门ID为当前用户的部门ID
        if (createReqVO.getCreateDeptId() == null) {
            createReqVO.setCreateDeptId(SecurityFrameworkUtils.getLoginUserDeptId());
        }
        // 判断是否存在相同显示顺序orderNo和业务类型bizType和相同的createDeptId
        BizFlowNodeDO bizFlowNode = bizFlowNodeService.getBizFlowNodeByOrderNoAndBizTypeAndCreateDeptId(
                createReqVO.getOrderNo(),
                createReqVO.getBizType(),
                createReqVO.getCreateDeptId()
        );
        if (bizFlowNode != null) {
            // 使用自定义错误信息，表示已存在相同的节点
            ErrorCode errorCode = new ErrorCode(1_009_001_100, "已存在相同顺序、业务类型和部门的流程节点");
            throw exception(errorCode);
        }
        // 设置审核人ID为当前登录用户
        return success(bizFlowNodeService.createBizFlowNode(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新业务流程节点配置")
    @PreAuthorize("@ss.hasPermission('demo:biz-flow-node:update')")
    public CommonResult<Boolean> updateBizFlowNode(@Valid @RequestBody BizFlowNodeSaveReqVO updateReqVO) {
        bizFlowNodeService.updateBizFlowNode(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除业务流程节点配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('demo:biz-flow-node:delete')")
    public CommonResult<Boolean> deleteBizFlowNode(@RequestParam("id") Long id) {
        bizFlowNodeService.deleteBizFlowNode(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得业务流程节点配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    // @PreAuthorize("@ss.hasPermission('demo:biz-flow-node:query')")
    public CommonResult<BizFlowNodeRespVO> getBizFlowNode(@RequestParam("id") Long id) {
        BizFlowNodeDO bizFlowNode = bizFlowNodeService.getBizFlowNode(id);
        return success(BeanUtils.toBean(bizFlowNode, BizFlowNodeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得业务流程节点配置分页")
    // @PreAuthorize("@ss.hasPermission('demo:biz-flow-node:query')")
    public CommonResult<PageResult<BizFlowNodeRespVO>> getBizFlowNodePage(@Valid BizFlowNodePageReqVO pageReqVO) {
        // 获取当前用户部门ID
        Long loginUserDeptId = SecurityFrameworkUtils.getLoginUserDeptId();

        // 如果未指定创建部门ID，则使用当前用户的部门ID
        if (pageReqVO.getCreateDeptId() == null && loginUserDeptId != null) {
            pageReqVO.setCreateDeptId(loginUserDeptId);
        }
        // 默认查询businessId为null的数据（未关联具体业务的流程节点配置）
        if (pageReqVO.getBusinessId() == null) {
            pageReqVO.setBusinessId(null); // 显式设置为null，确保查询条件正确
        }
        PageResult<BizFlowNodeDO> pageResult = bizFlowNodeService.getBizFlowNodePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BizFlowNodeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出业务流程节点配置 Excel")
    @PreAuthorize("@ss.hasPermission('demo:biz-flow-node:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportBizFlowNodeExcel(@Valid BizFlowNodePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        // 获取当前用户部门ID
        Long loginUserDeptId = SecurityFrameworkUtils.getLoginUserDeptId();

        // 如果未指定创建部门ID，则使用当前用户的部门ID
        if (pageReqVO.getCreateDeptId() == null && loginUserDeptId != null) {
            pageReqVO.setCreateDeptId(loginUserDeptId);
        }
        // 默认导出businessId为null的数据（未关联具体业务的流程节点配置）
        if (pageReqVO.getBusinessId() == null) {
            pageReqVO.setBusinessId(null); // 显式设置为null，确保查询条件正确
        }
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BizFlowNodeDO> list = bizFlowNodeService.getBizFlowNodePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "业务流程节点配置.xls", "数据", BizFlowNodeRespVO.class,
                        BeanUtils.toBean(list, BizFlowNodeRespVO.class));
    }

    @GetMapping("/list-by-dept-and-biztype")
    @Operation(summary = "根据部门ID和业务类型获取业务流程节点列表")
    @Parameter(name = "deptId", description = "部门ID", required = true, example = "1024")
    @Parameter(name = "bizType", description = "业务类型", required = true, example = "物资验收")
    public CommonResult<List<BizFlowNodeRespVO>> getBizFlowNodeListByDeptAndBizType(
            @RequestParam("deptId") Long deptId,
            @RequestParam("bizType") String bizType) {
        if (bizType.contains("出库")){
            bizType = "物资出库";
        }
        List<BizFlowNodeDO> list = bizFlowNodeService.getBizFlowNodeListByCreateDeptAndBizType(deptId, bizType);
        return success(BeanUtils.toBean(list, BizFlowNodeRespVO.class));
    }

    @GetMapping("/list-by-createdept-biztype")
    @Operation(summary = "根据创建部门ID和业务类型获取业务流程节点列表")
    @Parameter(name = "createDeptId", description = "创建部门ID", required = true, example = "1024")
    @Parameter(name = "bizType", description = "业务类型", required = true, example = "物资验收")
    public CommonResult<List<BizFlowNodeRespVO>> getBizFlowNodeListByCreateDeptAndBizType(
            @RequestParam("createDeptId") Long createDeptId,
            @RequestParam("bizType") String bizType) {
        List<BizFlowNodeDO> list = bizFlowNodeService.getBizFlowNodeListByCreateDeptAndBizType(createDeptId, bizType);
        return success(BeanUtils.toBean(list, BizFlowNodeRespVO.class));
    }

}
