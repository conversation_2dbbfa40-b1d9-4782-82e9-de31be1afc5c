package cn.iocoder.yudao.module.demo.controller.admin.bizflownode.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 业务流程节点配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BizFlowNodeRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3781")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("业务类型")
    private String bizType;

    @Schema(description = "节点名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("节点名称")
    private String nodeName;

    @Schema(description = "审批角色ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27604")
    @ExcelProperty("审批角色ID")
    private Long roleId;

    @Schema(description = "角色名称", example = "管理员")
    @ExcelProperty("角色名称")
    private String roleName;

    @Schema(description = "部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19363")
    @ExcelProperty("部门ID")
    private Long deptId;

    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("显示顺序")
    private Integer orderNo;

    @Schema(description = "是否通过", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("是否通过")
    private Integer isPass;

    @Schema(description = "关联业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    @ExcelProperty("关联业务ID")
    private Long businessId;

    @Schema(description = "审核人")
    @ExcelProperty("审核人")
    private String auditPerson;

    @Schema(description = "审核人签名")
    @ExcelProperty("审核人签名")
    private String auditSign;

    @Schema(description = "审批意见")
    @ExcelProperty("审批意见")
    private String auditReason;

    @Schema(description = "创建部门ID")
    @ExcelProperty("创建部门ID")
    private Long createDeptId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "审核人ID", example = "1024")
    private Long auditPersonId;

}