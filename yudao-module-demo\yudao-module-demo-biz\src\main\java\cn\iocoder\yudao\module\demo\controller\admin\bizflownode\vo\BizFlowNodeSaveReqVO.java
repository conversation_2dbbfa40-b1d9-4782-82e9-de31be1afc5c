package cn.iocoder.yudao.module.demo.controller.admin.bizflownode.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 业务流程节点配置新增/修改 Request VO")
@Data
public class BizFlowNodeSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3781")
    private Long id;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "业务类型不能为空")
    private String bizType;

    @Schema(description = "节点名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "节点名称不能为空")
    private String nodeName;

    @Schema(description = "审批角色ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27604")
    @NotNull(message = "审批角色ID不能为空")
    private Long roleId;

    @Schema(description = "角色名称", example = "管理员")
    private String roleName;

    @Schema(description = "部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19363")
    @NotNull(message = "部门ID不能为空")
    private Long deptId;

    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "显示顺序不能为空")
    private Integer orderNo;
    
    @Schema(description = "是否通过", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer isPass;
    
    @Schema(description = "关联业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long businessId;
    
    @Schema(description = "审核人")
    private String auditPerson;
    
    @Schema(description = "审核人签名")
    private String auditSign;

    @Schema(description = "审批意见")
    private String auditReason;

    @Schema(description = "创建部门ID")
    private Long createDeptId;

    @Schema(description = "审核人ID", example = "1024")
    private Long auditPersonId;

}