package cn.iocoder.yudao.module.demo.controller.admin.dispatchorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.time.LocalDateTime;

import cn.iocoder.yudao.module.demo.controller.admin.materialacceptdetail.vo.MaterialAcceptDetailSaveReqVO;

@Schema(description = "管理后台 - 调令信息新增/修改 Request VO")
@Data
public class DispatchOrderSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30182")
    private Long id;

    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "类型不能为空")
    private String type;

    @Schema(description = "被调单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "被调单位不能为空")
    private Long targetUnit;

    @Schema(description = "目的地", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "目的地不能为空")
    private String destination;

    @Schema(description = "部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26166")
    @NotNull(message = "部门ID不能为空")
    private Long deptId;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer status;
    
    @Schema(description = "是否签收", example = "false")
    private Boolean isSign;
    
    @Schema(description = "签收时间")
    private LocalDateTime signTime;
    
    @Schema(description = "签收人员ID")
    private Long signPersonId;

    @Schema(description = "物资详情集合")
    private List<MaterialAcceptDetailSaveReqVO> materialDetails;

}
