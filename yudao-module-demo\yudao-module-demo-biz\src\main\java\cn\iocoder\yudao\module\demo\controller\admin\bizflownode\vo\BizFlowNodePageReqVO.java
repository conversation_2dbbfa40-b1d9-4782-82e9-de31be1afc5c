package cn.iocoder.yudao.module.demo.controller.admin.bizflownode.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 业务流程节点配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BizFlowNodePageReqVO extends PageParam {

    @Schema(description = "业务类型", example = "2")
    private String bizType;

    @Schema(description = "节点名称", example = "赵六")
    private String nodeName;

    @Schema(description = "审批角色ID", example = "27604")
    private Long roleId;

    @Schema(description = "角色名称", example = "管理员")
    private String roleName;

    @Schema(description = "部门ID", example = "19363")
    private Long deptId;

    @Schema(description = "显示顺序")
    private Integer orderNo;

    @Schema(description = "是否通过", example = "1")
    private Integer isPass;

    @Schema(description = "关联业务ID", example = "1001")
    private Long businessId;
    
    @Schema(description = "审核人")
    private String auditPerson;

    @Schema(description = "审批意见")
    private String auditReason;

    @Schema(description = "创建部门ID")
    private Long createDeptId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}