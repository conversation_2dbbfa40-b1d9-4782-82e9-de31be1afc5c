package cn.iocoder.yudao.module.demo.controller.admin.inboundallocationrule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 入库分配规则 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InboundAllocationRuleRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6102")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("规则名称")
    private String ruleName;

    @Schema(description = "规则编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("规则编码")
    private String ruleCode;

    @Schema(description = "规则类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("规则类型")
    private String ruleType;

    @Schema(description = "优先级", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("优先级")
    private Integer priority;

    @Schema(description = "规则状态：1-启用，0-禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("规则状态：1-启用，0-禁用")
    private Integer ruleStatus;

    @Schema(description = "分类")
    @ExcelProperty("分类")
    private String category;

    @Schema(description = "名称", example = "王五")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "仓库名称", example = "李四")
    @ExcelProperty("仓库名称")
    private String warehouseName;

    @Schema(description = "垛位名称", example = "芋艿")
    @ExcelProperty("垛位名称")
    private String locationName;

    @Schema(description = "仓库ID", example = "19578")
    @ExcelProperty("仓库ID")
    private Long warehouseId;

    @Schema(description = "垛位ID", example = "24409")
    @ExcelProperty("垛位ID")
    private Long locationId;

    @Schema(description = "创建部门ID", example = "1764")
    @ExcelProperty("创建部门ID")
    private Long createDeptId;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}