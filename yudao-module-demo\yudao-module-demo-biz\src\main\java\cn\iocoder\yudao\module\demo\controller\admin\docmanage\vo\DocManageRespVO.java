package cn.iocoder.yudao.module.demo.controller.admin.docmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 文档管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DocManageRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23818")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "文档分类", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("文档分类")
    private String docCategory;

    @Schema(description = "文档描述")
    @ExcelProperty("文档描述")
    private String docDesc;

    @Schema(description = "关键词，逗号分隔", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("关键词，逗号分隔")
    private String keywords;

    @Schema(description = "文档所属（1=个人文档，2=公共文档，3=收藏文档）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("文档所属（1=个人文档，2=公共文档，3=收藏文档）")
    private Integer docBelong;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "文件id", example = "12957")
    @ExcelProperty("文件id")
    private String fileId;

}