package cn.iocoder.yudao.module.demo.controller.admin.checkstandard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 巡检项目 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MaterialCheckStandardRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12614")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "关联业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32018")
    @ExcelProperty("关联业务ID")
    private Long acceptId;

    @Schema(description = "检查项目")
    @ExcelProperty("检查项目")
    private String checkItem;

    @Schema(description = "检查标准")
    @ExcelProperty("检查标准")
    private String checkStandard;

    @Schema(description = "巡检结果")
    @ExcelProperty("巡检结果")
    private String checkResult;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}