package cn.iocoder.yudao.module.demo.controller.admin.dispatchorder.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 调令信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DispatchOrderPageReqVO extends PageParam {

    @Schema(description = "单号")
    private String orderNo;

    @Schema(description = "类型", example = "2")
    private String type;

    @Schema(description = "被调单位")
    private Long targetUnit;

    @Schema(description = "目的地")
    private String destination;

    @Schema(description = "部门ID", example = "26166")
    private Long deptId;

    @Schema(description = "部门ID集合", example = "26166")
    private List<Long> deptIdList;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "是否签收", example = "true")
    private Boolean isSign;

    @Schema(description = "签收人员ID")
    private Long signPersonId;

    @Schema(description = "签收时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] signTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
