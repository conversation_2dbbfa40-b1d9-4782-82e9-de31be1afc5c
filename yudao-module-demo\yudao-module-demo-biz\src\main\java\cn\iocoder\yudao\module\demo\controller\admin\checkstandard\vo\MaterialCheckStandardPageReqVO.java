package cn.iocoder.yudao.module.demo.controller.admin.checkstandard.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 巡检项目分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MaterialCheckStandardPageReqVO extends PageParam {

    @Schema(description = "关联业务ID", example = "32018")
    private Long acceptId;

    @Schema(description = "检查项目")
    private String checkItem;

    @Schema(description = "检查标准")
    private String checkStandard;
    
    @Schema(description = "巡检结果")
    private String checkResult;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}